import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

// Test configuration
const BASE_URL = 'http://localhost:5000';
const TEST_ROOM_ID = 'test-room-' + Date.now();

// Mock user credentials (you'll need to replace with actual test user)
const TEST_USER = {
  username: 'testuser',
  password: 'testpass123'
};

async function testCrossPlatformFileUpload() {
  try {
    console.log('🧪 Starting cross-platform file upload test...');
    
    // Step 1: Login to get session
    console.log('📝 Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/api/users/login`, TEST_USER);
    const cookies = loginResponse.headers['set-cookie'];
    
    if (!cookies) {
      throw new Error('No session cookies received from login');
    }
    
    console.log('✅ Login successful');
    
    // Step 2: Create a test room
    console.log('🏠 Creating test room...');
    const roomResponse = await axios.post(`${BASE_URL}/api/cross-platform/rooms`, {
      name: `Test Room ${Date.now()}`,
      allowedPlatforms: ['sociality', 'telegram', 'discord']
    }, {
      headers: {
        'Cookie': cookies.join('; ')
      }
    });
    
    const roomId = roomResponse.data.room.roomId;
    console.log('✅ Room created:', roomId);
    
    // Step 3: Create a test image file
    console.log('🖼️ Creating test image...');
    const testImagePath = path.join(process.cwd(), 'test-image.txt');
    fs.writeFileSync(testImagePath, 'This is a test file for cross-platform upload');
    
    // Step 4: Test file upload
    console.log('📤 Testing file upload...');
    const formData = new FormData();
    formData.append('message', 'Test message with file attachment');
    formData.append('file', fs.createReadStream(testImagePath));
    
    const uploadResponse = await axios.post(`${BASE_URL}/api/cross-platform/rooms/${roomId}/messages`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Cookie': cookies.join('; ')
      }
    });
    
    console.log('✅ File upload successful:', uploadResponse.data);
    
    // Step 5: Verify message was stored
    console.log('🔍 Verifying message storage...');
    const messagesResponse = await axios.get(`${BASE_URL}/api/cross-platform/rooms/${roomId}/messages`, {
      headers: {
        'Cookie': cookies.join('; ')
      }
    });
    
    const messages = messagesResponse.data.messages;
    const uploadedMessage = messages.find(msg => msg.text === 'Test message with file attachment');
    
    if (!uploadedMessage) {
      throw new Error('Uploaded message not found in room messages');
    }
    
    if (!uploadedMessage.file) {
      throw new Error('File URL not found in message');
    }
    
    console.log('✅ Message verification successful:', {
      messageId: uploadedMessage.id,
      text: uploadedMessage.text,
      hasFile: !!uploadedMessage.file,
      fileName: uploadedMessage.fileName,
      fileSize: uploadedMessage.fileSize
    });
    
    // Cleanup
    fs.unlinkSync(testImagePath);
    console.log('🧹 Cleanup completed');
    
    console.log('🎉 All tests passed! Cross-platform file upload is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    process.exit(1);
  }
}

// Run the test
testCrossPlatformFileUpload();
