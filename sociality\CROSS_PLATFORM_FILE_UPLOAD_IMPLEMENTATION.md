# Cross-Platform File Upload Implementation

## Overview
This document describes the implementation of file upload functionality for cross-platform messaging in the Sociality application. Previously, the cross-platform messaging system only supported text messages. Now it supports images, files, and emojis.

## Changes Made

### 1. Backend Model Updates

#### CrossPlatformMessage Model (`sociality/backend/models/crossPlatformMessageModel.js`)
- Added new fields to support file uploads:
  - `img`: String field for image URLs
  - `file`: String field for file URLs  
  - `fileName`: String field for original file names
  - `fileSize`: Number field for file sizes in bytes
  - `emoji`: String field for emoji messages
- Made `text` field optional (not required) to allow messages with only files/images

### 2. Backend Route Updates

#### Cross-Platform Routes (`sociality/backend/routes/crossPlatformRoutes.js`)
- Added multer configuration for file uploads with 50MB limit
- Updated POST `/api/cross-platform/rooms/:roomId/messages` endpoint:
  - Added multer middleware to handle file uploads
  - Added support for `img` and `file` fields
  - Added Cloudinary integration for file storage
  - Updated message validation to accept messages with files/images/emojis
  - Enhanced response format to include file information

- Updated GET `/api/cross-platform/rooms/:roomId/messages` endpoint:
  - Modified message formatting to include file fields in responses

- Updated relay endpoints (`/relay` and `/relay-direct`):
  - Enhanced message storage to handle file fields from other platforms
  - Updated socket message emission to include file information

### 3. Frontend Updates

#### MessageInput Component (`sociality/frontend/src/components/MessageInput.jsx`)
- Removed restriction that prevented file uploads in cross-platform rooms
- Updated federated message sending logic:
  - Added support for FormData when files are present
  - Modified field mapping for federated messages (uses "message" instead of "text")
  - Enhanced request handling to support both text-only and file messages

## Technical Details

### File Upload Flow
1. User selects file/image in cross-platform room
2. Frontend creates FormData with appropriate fields
3. Backend receives multipart form data via multer
4. Files are uploaded to Cloudinary for storage
5. Message is stored in database with file URLs
6. Message is emitted to connected users via Socket.IO
7. Message is relayed to other platforms via federation registry

### Field Mapping
- **Regular messages**: Use `text` field and `recipientId`
- **Federated messages**: Use `message` field (no `recipientId`)
- **File uploads**: Use FormData for both regular and federated messages

### File Storage
- All files are stored in Cloudinary
- Images use default Cloudinary settings
- Other files use `resource_type: 'auto'` and `folder: 'cross_platform_files'`

## Testing

A test script has been created at `sociality/backend/test/cross-platform-upload-test.js` to verify:
- User authentication
- Room creation
- File upload functionality
- Message storage verification
- File URL generation

## Supported File Types
- Images: All image formats supported by Cloudinary
- Files: All file types up to 50MB
- Emojis: Unicode emoji characters

## Security Considerations
- File size limit: 50MB per message
- All files are processed through Cloudinary
- User authentication required for all operations
- Room access control maintained

## Future Enhancements
- Add file type restrictions if needed
- Implement file compression for large images
- Add progress indicators for large file uploads
- Support for multiple files per message
- File preview generation for non-image files

## Usage
Users can now upload images and files in cross-platform rooms just like in regular conversations. The attachment menu provides options for both photos and files, and the uploads are automatically synchronized across all connected platforms.
