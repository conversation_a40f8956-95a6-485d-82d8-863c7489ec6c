const DiscordBinding = require('../models/DiscordBinding');

// Relay message to Discord
async function relayMessageToDiscord(client, roomId, message, roomToChannelMap) {
  try {
    // Find Discord channel bound to this room
    const binding = await DiscordBinding.findOne({
      $or: [
        { platformRoomId: roomId },
        { roomId: roomId }
      ]
    });

    if (!binding) {
      // Check in-memory map as fallback
      const channelId = roomToChannelMap.get(roomId);
      if (!channelId) {
        console.log(`No Discord binding found for room ${roomId}`);
        return;
      }

      // Send message using in-memory mapping
      const channel = await client.channels.fetch(channelId);
      if (channel) {
        await sendMessageToDiscord(channel, message);
        console.log(`📨 Message relayed to Discord channel ${channelId} via in-memory mapping from ${message.from?.platform || 'unknown'}`);
      }
      return;
    }

    // Send message using database binding
    const channel = await client.channels.fetch(binding.discordChannelId);
    if (!channel) {
      console.log(`Discord channel ${binding.discordChannelId} not found`);
      return;
    }

    await sendMessageToDiscord(channel, message);

    // Update last used timestamp
    binding.lastUsedAt = new Date();
    await binding.save();

    console.log(`📨 Message relayed to Discord channel ${binding.discordChannelId} for room ${roomId} from ${message.from?.platform || 'unknown'}`);
  } catch (error) {
    console.error('❌ Error relaying message to Discord:', error.message);
    throw error;
  }
}

// Format message for Discord display
function formatMessageForDiscord(message) {
  const platform = message.from?.platform || 'unknown';
  const displayName = message.from?.displayName || message.from?.username || message.from?.userId || 'Unknown User';
  const platformEmoji = getPlatformEmoji(platform);

  let messageText = '';
  if (message.text) {
    messageText = message.text;
  } else if (message.img) {
    messageText = '[Image]';
  } else if (message.file) {
    messageText = `[File: ${message.fileName || 'Document'}]`;
  } else if (message.emoji) {
    messageText = message.emoji;
  } else {
    messageText = '[Media]';
  }

  return `${platformEmoji} **${displayName}**: ${messageText}`;
}

// Get emoji for platform
function getPlatformEmoji(platform) {
  const emojiMap = {
    'sociality': '🌐',
    'platform-a': '🌐',
    'telegram': '📱',
    'platform-b': '📱',
    'discord': '🎮',
    'platform-c': '🎮',
    'web': '💻',
    'mobile': '📱'
  };

  return emojiMap[platform] || '💬';
}

// Validate Discord binding
async function validateDiscordBinding(client, binding) {
  try {
    // Try to fetch channel to validate the binding
    const channel = await client.channels.fetch(binding.discordChannelId);

    if (channel) {
      binding.isValid = true;
      binding.lastValidatedAt = new Date();
      await binding.save();
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Discord binding validation failed for channel ${binding.discordChannelId}:`, error.message);

    // Mark binding as invalid
    binding.isValid = false;
    binding.lastValidatedAt = new Date();
    await binding.save();

    return false;
  }
}

// Load all Discord bindings and validate them
async function loadAndValidateDiscordBindings(client, channelToRoomMap, roomToChannelMap) {
  try {
    const bindings = await DiscordBinding.find({ isValid: true });

    console.log(`Loading ${bindings.length} Discord bindings...`);

    for (const binding of bindings) {
      // Add to in-memory maps
      const roomId = binding.roomId || binding.platformRoomId;
      channelToRoomMap.set(binding.discordChannelId, roomId);
      roomToChannelMap.set(roomId, binding.discordChannelId);

      // Validate binding in background
      validateDiscordBinding(client, binding).catch(error => {
        console.error(`Background validation failed for binding ${binding._id}:`, error.message);
      });
    }

    console.log(`Loaded ${bindings.length} Discord bindings into memory`);
  } catch (error) {
    console.error('Error loading Discord bindings:', error.message);
  }
}

// Send message to Discord with file support
async function sendMessageToDiscord(channel, message) {
  try {
    const platform = message.from?.platform || 'unknown';
    const displayName = message.from?.displayName || message.from?.username || message.from?.userId || 'Unknown User';
    const platformEmoji = getPlatformEmoji(platform);

    // Handle different message types
    if (message.img) {
      // Send image with text
      const messageText = message.text ?
        `${platformEmoji} **${displayName}**: ${message.text}` :
        `${platformEmoji} **${displayName}** shared an image`;

      await channel.send({
        content: messageText,
        files: [{ attachment: message.img, name: 'image.jpg' }]
      });
    } else if (message.file) {
      // Send file with text
      const messageText = message.text ?
        `${platformEmoji} **${displayName}**: ${message.text}` :
        `${platformEmoji} **${displayName}** shared a file: ${message.fileName || 'Document'}`;

      await channel.send({
        content: messageText,
        files: [{ attachment: message.file, name: message.fileName || 'document' }]
      });
    } else if (message.emoji) {
      // Send emoji message
      await channel.send(`${platformEmoji} **${displayName}**: ${message.emoji}`);
    } else if (message.text) {
      // Send text message
      const formattedMessage = formatMessageForDiscord(message);
      await channel.send(formattedMessage);
    }
  } catch (error) {
    console.error('Error sending message to Discord:', error);
    // Fallback to text message if file sending fails
    try {
      const formattedMessage = formatMessageForDiscord(message);
      await channel.send(formattedMessage);
    } catch (fallbackError) {
      console.error('Fallback message also failed:', fallbackError);
      throw fallbackError;
    }
  }
}

module.exports = {
  relayMessageToDiscord,
  formatMessageForDiscord,
  getPlatformEmoji,
  validateDiscordBinding,
  loadAndValidateDiscordBindings,
  sendMessageToDiscord
};
