const TelegramBinding = require('../models/TelegramBinding');

// Relay message to Telegram
async function relayMessageToTelegram(bot, roomId, message, roomToChatMap) {
  try {
    // Find Telegram chat bound to this room
    const binding = await TelegramBinding.findOne({
      $or: [
        { platformRoomId: roomId },
        { roomId: roomId }
      ]
    });

    if (!binding) {
      // Check in-memory map as fallback
      const chatId = roomToChatMap.get(roomId);
      if (!chatId) {
        console.log(`No Telegram binding found for room ${roomId}`);
        return;
      }

      // Send message using in-memory mapping
      await sendMessageToTelegram(bot, chatId, message);
      console.log(`Message relayed to Telegram chat ${chatId} via in-memory mapping`);
      return;
    }

    // Send message using database binding
    await sendMessageToTelegram(bot, binding.telegramChatId, message);

    // Update last used timestamp
    binding.lastUsedAt = new Date();
    await binding.save();

    console.log(`Message relayed to Telegram chat ${binding.telegramChatId} for room ${roomId}`);
  } catch (error) {
    console.error('Error relaying message to Telegram:', error.message);
    throw error;
  }
}

// Format message for Telegram display
function formatMessageForTelegram(message) {
  const platform = message.from?.platform || 'unknown';
  const displayName = message.from?.displayName || message.from?.username || message.from?.userId || 'Unknown User';
  const platformEmoji = getPlatformEmoji(platform);

  let messageText = '';
  if (message.text) {
    messageText = message.text;
  } else if (message.img) {
    messageText = '[Image]';
  } else if (message.file) {
    messageText = `[File: ${message.fileName || 'Document'}]`;
  } else if (message.emoji) {
    messageText = message.emoji;
  } else {
    messageText = '[Media]';
  }

  return `${platformEmoji} **${displayName}**: ${messageText}`;
}

// Get emoji for platform
function getPlatformEmoji(platform) {
  const emojiMap = {
    'sociality': '🌐',
    'platform-a': '🌐',
    'discord': '🎮',
    'platform-c': '🎮',
    'telegram': '📱',
    'platform-b': '📱',
    'web': '💻',
    'mobile': '📱'
  };

  return emojiMap[platform] || '💬';
}

// Validate Telegram binding
async function validateTelegramBinding(bot, binding) {
  try {
    // Try to get chat info to validate the binding
    const chat = await bot.getChat(binding.telegramChatId);

    if (chat) {
      binding.isValid = true;
      binding.lastValidatedAt = new Date();
      await binding.save();
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Telegram binding validation failed for chat ${binding.telegramChatId}:`, error.message);

    // Mark binding as invalid
    binding.isValid = false;
    binding.lastValidatedAt = new Date();
    await binding.save();

    return false;
  }
}

// Load all Telegram bindings and validate them
async function loadAndValidateTelegramBindings(bot, chatToRoomMap, roomToChatMap) {
  try {
    const bindings = await TelegramBinding.find({ isValid: true });

    console.log(`Loading ${bindings.length} Telegram bindings...`);

    for (const binding of bindings) {
      // Add to in-memory maps
      chatToRoomMap.set(binding.telegramChatId, binding.roomId || binding.platformRoomId);
      roomToChatMap.set(binding.roomId || binding.platformRoomId, binding.telegramChatId);

      // Validate binding in background
      validateTelegramBinding(bot, binding).catch(error => {
        console.error(`Background validation failed for binding ${binding._id}:`, error.message);
      });
    }

    console.log(`Loaded ${bindings.length} Telegram bindings into memory`);
  } catch (error) {
    console.error('Error loading Telegram bindings:', error.message);
  }
}

// Send message to Telegram with file support
async function sendMessageToTelegram(bot, chatId, message) {
  try {
    const platform = message.from?.platform || 'unknown';
    const displayName = message.from?.displayName || message.from?.username || message.from?.userId || 'Unknown User';
    const platformEmoji = getPlatformEmoji(platform);

    // Handle different message types
    if (message.img) {
      // Send image with caption
      const caption = message.text ?
        `${platformEmoji} **${displayName}**: ${message.text}` :
        `${platformEmoji} **${displayName}** shared an image`;

      await bot.sendPhoto(chatId, message.img, {
        caption,
        parse_mode: 'Markdown'
      });
    } else if (message.file) {
      // Send file with caption
      const caption = message.text ?
        `${platformEmoji} **${displayName}**: ${message.text}` :
        `${platformEmoji} **${displayName}** shared a file: ${message.fileName || 'Document'}`;

      await bot.sendDocument(chatId, message.file, {
        caption,
        parse_mode: 'Markdown'
      });
    } else if (message.emoji) {
      // Send emoji message
      await bot.sendMessage(chatId, `${platformEmoji} **${displayName}**: ${message.emoji}`, {
        parse_mode: 'Markdown'
      });
    } else if (message.text) {
      // Send text message
      const formattedMessage = formatMessageForTelegram(message);
      await bot.sendMessage(chatId, formattedMessage, {
        parse_mode: 'Markdown'
      });
    }
  } catch (error) {
    console.error('Error sending message to Telegram:', error);
    // Fallback to text message if file sending fails
    try {
      const formattedMessage = formatMessageForTelegram(message);
      await bot.sendMessage(chatId, formattedMessage);
    } catch (fallbackError) {
      console.error('Fallback message also failed:', fallbackError);
      throw fallbackError;
    }
  }
}

module.exports = {
  relayMessageToTelegram,
  formatMessageForTelegram,
  getPlatformEmoji,
  validateTelegramBinding,
  loadAndValidateTelegramBindings,
  sendMessageToTelegram
};
