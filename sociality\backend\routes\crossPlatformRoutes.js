import express from "express";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import mongoose from "mongoose";
import multer from "multer";
import os from "os";
import Room from "../models/roomModel.js";
import CrossPlatformMessage from "../models/crossPlatformMessageModel.js";
import TelegramBinding from "../models/telegramBindingModel.js";
import DiscordBinding from "../models/discordBindingModel.js";
import protectRoute from "../middlewares/protectRoute.js";
import { v2 as cloudinary } from "cloudinary";

// Configure multer for file uploads
const storage = multer.diskStorage({
	destination: function (req, file, cb) {
		// Use OS temp directory for cross-platform compatibility
		cb(null, os.tmpdir())
	},
	filename: function (req, file, cb) {
		cb(null, Date.now() + '-' + file.originalname)
	}
});

const upload = multer({
	storage: storage,
	limits: {
		fileSize: 50 * 1024 * 1024 // 50MB limit for messages
	},
	fileFilter: function (req, file, cb) {
		// Accept all file types for cross-platform messages
		cb(null, true);
	}
});

const router = express.Router();

// Federation Registry URL
const FEDERATION_REGISTRY_URL = process.env.FEDERATION_REGISTRY_URL || 'http://localhost:7300';
const PLATFORM_URL = process.env.PLATFORM_URL || 'http://localhost:5000';

// Register platform with federation registry
const registerWithFederation = async () => {
  try {
    await axios.post(`${FEDERATION_REGISTRY_URL}/federation/peers`, {
      name: 'sociality',
      url: PLATFORM_URL
    });
    console.log('✅ Registered with federation registry');
  } catch (error) {
    console.warn('⚠️ Failed to register with federation registry:', error.message);
  }
};

// Create a cross-platform room with UUID
router.post("/rooms", protectRoute, async (req, res) => {
  try {
    const { name, allowedPlatforms = ['sociality', 'telegram', 'discord'] } = req.body;
    const userId = req.user?._id;
    const username = req.user?.username;

    console.log('Creating cross-platform room:', { name, userId, username, allowedPlatforms });

    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Room name is required'
      });
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Generate UUID for cross-platform room
    const roomId = uuidv4();

    // Create room in local database (private by default)
    const room = new Room({
      roomId,
      name,
      creator: userId,
      participants: userId ? [{ user: userId, role: 'admin' }] : [],
      settings: {
        isPrivate: true,
        requireApproval: false,
        maxParticipants: 100
      },
      federationSettings: {
        isEnabled: true,
        allowedPlatforms,
        registeredPeers: [],
        lastSyncAt: new Date()
      }
    });

    await room.save();
    console.log('Room saved to database:', { roomId, name, creator: userId });

    // Register room with federation registry
    try {
      const federationResponse = await axios.post(`${FEDERATION_REGISTRY_URL}/federation/rooms`, {
        roomId,
        name,
        peerUrl: PLATFORM_URL
      });
      console.log('Room registered with federation registry:', federationResponse.data);
    } catch (federationError) {
      console.warn('Failed to register room with federation registry:', federationError.message);
    }

    const responseData = {
      success: true,
      room: {
        roomId,
        name,
        creator: userId,
        federationSettings: room.federationSettings
      }
    };

    console.log('Sending room creation response:', responseData);
    res.status(201).json(responseData);
  } catch (error) {
    console.error('Error creating cross-platform room:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create cross-platform room',
      message: error.message
    });
  }
});

// Get cross-platform rooms for authenticated user
router.get("/rooms", protectRoute, async (req, res) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Get local rooms where user is a participant and federation is enabled
    const userRooms = await Room.find({
      'federationSettings.isEnabled': true,
      'participants.user': userId
    })
      .populate('creator', 'username name profilePic')
      .lean();

    // Format rooms for response
    const formattedRooms = userRooms.map(room => ({
      roomId: room.roomId,
      name: room.name,
      creator: room.creator,
      peers: room.federationSettings.registeredPeers || [],
      participantCount: room.participants?.length || 0,
      isPrivate: room.settings?.isPrivate || false,
      lastActivity: room.lastActivity
    }));

    res.json({
      success: true,
      rooms: formattedRooms
    });
  } catch (error) {
    console.error('Error fetching cross-platform rooms:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch cross-platform rooms',
      message: error.message
    });
  }
});

// Join a cross-platform room by room ID
router.post("/rooms/:roomId/join", protectRoute, async (req, res) => {
  try {
    const { roomId } = req.params;
    const userId = req.user?._id;
    const username = req.user?.username;

    if (!userId || !username) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Find the room locally first
    let room = await Room.findOne({ roomId });

    if (!room) {
      // For private rooms, we need to check if the room exists elsewhere
      // Try to get room info from federation registry
      try {
        const response = await axios.get(`${FEDERATION_REGISTRY_URL}/federation/rooms/${roomId}`);
        const federatedRoom = response.data;

        if (federatedRoom) {
          // Create a local copy of the federated room
          room = new Room({
            roomId,
            name: federatedRoom.name || `Room ${roomId}`,
            creator: new mongoose.Types.ObjectId('000000000000000000000000'), // System user for federated rooms
            participants: [],
            settings: {
              isPrivate: true,
              requireApproval: false,
              maxParticipants: 100
            },
            federationSettings: {
              isEnabled: true,
              allowedPlatforms: ['sociality', 'telegram', 'discord'],
              registeredPeers: federatedRoom.peers || [],
              lastSyncAt: new Date()
            }
          });
        } else {
          return res.status(404).json({
            success: false,
            error: 'Room not found. Please check the room ID.'
          });
        }
      } catch (federationError) {
        return res.status(404).json({
          success: false,
          error: 'Room not found. Please check the room ID.'
        });
      }
    }

    // Check if user is already a participant
    const existingParticipant = room.participants.find(p => p.user.toString() === userId.toString());
    if (!existingParticipant) {
      room.participants.push({ user: userId, role: 'member' });
      await room.save();
    }

    res.json({
      success: true,
      message: 'Successfully joined cross-platform room',
      room: {
        roomId: room.roomId,
        name: room.name,
        participantCount: room.participants.length,
        isPrivate: room.settings?.isPrivate || false
      }
    });
  } catch (error) {
    console.error('Error joining cross-platform room:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to join cross-platform room',
      message: error.message
    });
  }
});

// Delete a cross-platform room
router.delete("/rooms/:roomId", protectRoute, async (req, res) => {
  try {
    const { roomId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Find the room
    const room = await Room.findOne({ roomId });
    if (!room) {
      return res.status(404).json({
        success: false,
        error: 'Room not found'
      });
    }

    // Check if user is the creator or admin
    const userParticipant = room.participants.find(p => p.user.toString() === userId.toString());
    if (!userParticipant || (userParticipant.role !== 'admin' && room.creator.toString() !== userId.toString())) {
      return res.status(403).json({
        success: false,
        error: 'Only room creators and admins can delete rooms'
      });
    }

    // Delete all messages in the room
    await CrossPlatformMessage.deleteMany({ roomId });

    // Remove room from federation registry
    try {
      await axios.delete(`${FEDERATION_REGISTRY_URL}/federation/rooms/${roomId}`);
      console.log('Room removed from federation registry');
    } catch (federationError) {
      console.warn('Failed to remove room from federation registry:', federationError.message);
    }

    // Delete the room
    await Room.deleteOne({ roomId });

    res.json({
      success: true,
      message: 'Room deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting cross-platform room:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete room',
      message: error.message
    });
  }
});

// Get messages from a cross-platform room
router.get("/rooms/:roomId/messages", protectRoute, async (req, res) => {
  try {
    const { roomId } = req.params;
    const userId = req.user?._id;
    const limit = parseInt(req.query.limit) || 50;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Find the room locally
    const room = await Room.findOne({ roomId });
    if (!room) {
      return res.json({
        success: true,
        messages: []
      });
    }

    // Check if user is a participant in private rooms
    if (room.settings?.isPrivate) {
      const isParticipant = room.participants.some(p => p.user.toString() === userId.toString());
      if (!isParticipant) {
        return res.status(403).json({
          success: false,
          error: 'Access denied. You must join this room to view messages.'
        });
      }
    }

    // Get local cross-platform messages for this room
    const localMessages = await CrossPlatformMessage.find({ roomId })
      .sort({ createdAt: -1 })
      .limit(limit)
      .lean();

    // Transform messages to a consistent format
    const formattedMessages = localMessages.map(msg => ({
      id: msg._id.toString(),
      text: msg.text,
      img: msg.img || undefined,
      file: msg.file || undefined,
      fileName: msg.fileName || undefined,
      fileSize: msg.fileSize || undefined,
      emoji: msg.emoji || undefined,
      sender: {
        _id: msg.sender,
        username: msg.senderUsername,
        platform: msg.senderPlatform
      },
      timestamp: msg.createdAt,
      roomId: msg.roomId,
      platform: msg.platform
    }));

    res.json({
      success: true,
      messages: formattedMessages.reverse() // Return in chronological order
    });
  } catch (error) {
    console.error('Error fetching cross-platform room messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch room messages',
      message: error.message
    });
  }
});

// Send message to cross-platform room
router.post("/rooms/:roomId/messages", protectRoute, upload.fields([
	{ name: 'img', maxCount: 1 },
	{ name: 'file', maxCount: 1 }
]), async (req, res) => {
  try {
    const { roomId } = req.params;
    const { message, text, emoji, fileName, fileSize } = req.body;
    const userId = req.user?._id;
    const username = req.user?.username;
    const displayName = req.user?.name || username;

    console.log(`Sending cross-platform message to room ${roomId}:`, {
      message: message || text,
      userId,
      username,
      displayName,
      hasFiles: !!req.files,
      files: req.files ? Object.keys(req.files) : []
    });

    // Handle file uploads
    let img = "";
    let file = "";
    let finalFileName = fileName || "";
    let finalFileSize = parseInt(fileSize) || 0;

    if (req.files) {
      if (req.files.img && req.files.img[0]) {
        const upload = await cloudinary.uploader.upload(req.files.img[0].path);
        img = upload.secure_url;
        console.log("Uploaded image to cloudinary:", img);
      }
      if (req.files.file && req.files.file[0]) {
        const fileUpload = req.files.file[0];
        const upload = await cloudinary.uploader.upload(fileUpload.path, {
          resource_type: 'auto',
          folder: 'cross_platform_files'
        });
        file = upload.secure_url;
        finalFileName = finalFileName || fileUpload.originalname;
        finalFileSize = finalFileSize || fileUpload.size;
        console.log("Uploaded file to cloudinary:", file);
      }
    }

    const messageText = message || text || "";

    // Validate that we have some content
    if (!messageText && !img && !file && !emoji) {
      return res.status(400).json({
        success: false,
        error: 'Message content, image, file, or emoji is required'
      });
    }

    if (!userId || !username) {
      return res.status(400).json({
        success: false,
        error: 'Authentication is required'
      });
    }

    // Check if user has access to this room
    const room = await Room.findOne({ roomId });
    if (room && room.settings?.isPrivate) {
      const isParticipant = room.participants.some(p => p.user.toString() === userId.toString());
      if (!isParticipant) {
        return res.status(403).json({
          success: false,
          error: 'Access denied. You must join this room to send messages.'
        });
      }
    }

    // Prepare message for federation
    const federatedMessage = {
      from: {
        userId: userId.toString(),
        displayName,
        platform: 'sociality'
      },
      text: messageText,
      img: img || undefined,
      file: file || undefined,
      fileName: finalFileName || undefined,
      fileSize: finalFileSize || undefined,
      emoji: emoji || undefined,
      sentAt: new Date()
    };

    // Store the message locally first
    const localMessage = new CrossPlatformMessage({
      roomId,
      sender: userId.toString(),
      senderUsername: displayName,
      senderPlatform: 'sociality',
      text: messageText,
      img: img || "",
      file: file || "",
      fileName: finalFileName || "",
      fileSize: finalFileSize || 0,
      emoji: emoji || "",
      platform: 'sociality',
      messageId: Date.now().toString()
    });
    await localMessage.save();
    console.log(`Saved local message:`, localMessage);

    // Emit the message to connected Sociality users immediately via Socket.IO
    const io = req.app.get('io');
    if (io) {
      const socketMessage = {
        id: localMessage._id.toString(),
        text: messageText,
        img: img || undefined,
        file: file || undefined,
        fileName: finalFileName || undefined,
        fileSize: finalFileSize || undefined,
        emoji: emoji || undefined,
        sender: {
          _id: userId.toString(),
          username: displayName,
          platform: 'sociality'
        },
        timestamp: localMessage.createdAt.toISOString(),
        roomId,
        isCrossPlatform: true,
        platform: 'sociality'
      };

      console.log(`Emitting crossPlatformMessage to room_${roomId}:`, socketMessage);
      io.to(`room_${roomId}`).emit('crossPlatformMessage', socketMessage);
    } else {
      console.warn('Socket.IO instance not available');
    }

    // Note: Removed direct platform relay to prevent duplicate messages
    // All cross-platform communication now goes through federation registry only

    // Send to federation registry for relay to other platforms
    try {
      const relayResponse = await axios.post(`${FEDERATION_REGISTRY_URL}/federation/relay-message`, {
        roomId,
        message: federatedMessage,
        originatingPlatform: PLATFORM_URL
      });

      console.log(`Federation relay response:`, relayResponse.data);

      res.json({
        success: true,
        message: 'Message sent to cross-platform room',
        localMessage: {
          id: localMessage._id.toString(),
          text: messageText,
          img: img || undefined,
          file: file || undefined,
          fileName: finalFileName || undefined,
          fileSize: finalFileSize || undefined,
          emoji: emoji || undefined,
          sender: {
            _id: userId.toString(),
            username: displayName,
            platform: 'sociality'
          },
          timestamp: localMessage.createdAt.toISOString(),
          roomId,
          platform: 'sociality'
        },
        relayResults: relayResponse.data.results
      });
    } catch (relayError) {
      console.error('Failed to relay message:', relayError.message);

      // Still return success since local message was saved and emitted
      res.json({
        success: true,
        message: 'Message sent locally but failed to relay to other platforms',
        localMessage: {
          id: localMessage._id.toString(),
          text: messageText,
          img: img || undefined,
          file: file || undefined,
          fileName: finalFileName || undefined,
          fileSize: finalFileSize || undefined,
          emoji: emoji || undefined,
          sender: {
            _id: userId.toString(),
            username: displayName,
            platform: 'sociality'
          },
          timestamp: localMessage.createdAt.toISOString(),
          roomId,
          platform: 'sociality'
        },
        relayError: relayError.message
      });
    }
  } catch (error) {
    console.error('Error sending cross-platform message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send cross-platform message',
      message: error.message
    });
  }
});

// Delete a cross-platform message
router.delete("/messages/:messageId", protectRoute, async (req, res) => {
  try {
    const { messageId } = req.params;
    const { deleteForEveryone } = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const message = await CrossPlatformMessage.findById(messageId);

    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found'
      });
    }

    // Check if this is the user's message
    const isOwnMessage = message.sender.toString() === userId.toString();

    if (deleteForEveryone) {
      // Only message sender can delete for everyone
      if (!isOwnMessage) {
        return res.status(403).json({
          success: false,
          error: 'You can only delete your own messages for everyone'
        });
      }

      // Delete files from Cloudinary if they exist
      if (message.img) {
        try {
          const imgId = message.img.split("/").pop().split(".")[0];
          await cloudinary.uploader.destroy(imgId);
          console.log('Deleted image from Cloudinary:', imgId);
        } catch (error) {
          console.warn('Failed to delete image from Cloudinary:', error.message);
        }
      }

      if (message.file) {
        try {
          const fileId = message.file.split("/").pop().split(".")[0];
          await cloudinary.uploader.destroy(fileId, { resource_type: 'auto' });
          console.log('Deleted file from Cloudinary:', fileId);
        } catch (error) {
          console.warn('Failed to delete file from Cloudinary:', error.message);
        }
      }

      // Mark as deleted for everyone
      message.deletedForEveryone = true;
      await message.save();

      // Emit deletion event to all room participants
      const io = req.app.get('io');
      if (io) {
        io.to(message.roomId).emit('crossPlatformMessageDeleted', {
          messageId: message._id,
          deleteForEveryone: true
        });
      }

      console.log(`Cross-platform message ${messageId} deleted for everyone by user ${userId}`);
    } else {
      // Delete just for this user
      if (!message.deletedFor.includes(userId)) {
        message.deletedFor.push(userId);
        await message.save();
      }

      console.log(`Cross-platform message ${messageId} deleted for user ${userId}`);
    }

    res.status(200).json({
      success: true,
      message: 'Message deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting cross-platform message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete message'
    });
  }
});

// Relay endpoint for receiving messages from federation registry
router.post("/relay", async (req, res) => {
  try {
    const { roomId, message } = req.body;

    if (!roomId || !message) {
      return res.status(400).json({
        success: false,
        error: 'Room ID and message are required'
      });
    }

    console.log(`📨 Received relayed message for room ${roomId} from ${message.from?.platform || 'unknown platform'}`);

    // Find the room
    let room = await Room.findOne({ roomId });

    if (!room) {
      // Create a local copy of the federated room if it doesn't exist
      // Use a system user ID for cross-platform rooms
      const systemUserId = new mongoose.Types.ObjectId('000000000000000000000000');
      room = new Room({
        roomId,
        name: `Federated Room ${roomId}`,
        creator: systemUserId,
        participants: [],
        federationSettings: {
          isEnabled: true,
          allowedPlatforms: ['sociality', 'telegram', 'discord'],
          registeredPeers: [],
          lastSyncAt: new Date()
        }
      });
      await room.save();
    }

    // Store the message locally with flexible field mapping
    const crossPlatformMessage = new CrossPlatformMessage({
      roomId,
      sender: message.from?.userId || message.from?.id || 'unknown',
      senderUsername: message.from?.displayName || message.from?.username || 'Unknown User',
      senderPlatform: message.from?.platform || 'unknown',
      text: message.text || "",
      img: message.img || "",
      file: message.file || "",
      fileName: message.fileName || "",
      fileSize: message.fileSize || 0,
      emoji: message.emoji || "",
      platform: message.from?.platform || 'unknown',
      messageId: message.id || Date.now().toString(),
      relayedFrom: req.body.originatingPlatform
    });
    await crossPlatformMessage.save();

    // Update room's last activity
    room.lastActivity = new Date();
    room.messageCount = (room.messageCount || 0) + 1;
    await room.save();

    // Emit the message to connected Sociality users via Socket.IO
    const io = req.app.get('io');
    if (io) {
      const socketMessage = {
        id: message.id || Date.now().toString(),
        text: message.text,
        img: message.img || undefined,
        file: message.file || undefined,
        fileName: message.fileName || undefined,
        fileSize: message.fileSize || undefined,
        emoji: message.emoji || undefined,
        sender: {
          _id: message.from?.userId || message.from?.id || 'unknown',
          username: message.from?.displayName || message.from?.username || 'Unknown User',
          platform: message.from?.platform || 'unknown'
        },
        timestamp: message.sentAt || message.timestamp || new Date().toISOString(),
        roomId,
        isCrossPlatform: true,
        platform: message.from?.platform || 'unknown'
      };

      console.log(`🔄 Emitting crossPlatformMessage to room_${roomId} from ${message.from?.platform}:`, socketMessage);
      io.to(`room_${roomId}`).emit('crossPlatformMessage', socketMessage);
    } else {
      console.warn('⚠️ Socket.IO instance not available for message relay');
    }

    res.json({
      success: true,
      message: 'Message relayed to Sociality users'
    });
  } catch (error) {
    console.error('Error handling relayed message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to handle relayed message',
      message: error.message
    });
  }
});

// Cross-platform relay endpoint for direct platform-to-platform communication
router.post("/relay-direct", async (req, res) => {
  try {
    const { roomId, message, originatingPlatform } = req.body;

    if (!roomId || !message) {
      return res.status(400).json({
        success: false,
        error: 'Room ID and message are required'
      });
    }

    console.log(`📨 Received direct relay message for room ${roomId} from ${message.from?.platform || 'unknown platform'}`);

    // Skip if message is from Sociality to avoid loops
    if (message.from?.platform === 'sociality') {
      console.log('⏭️ Skipping message from Sociality to avoid loop');
      return res.json({ success: true, message: 'Message skipped (same platform)' });
    }

    // Find the room
    let room = await Room.findOne({ roomId });

    if (!room) {
      // Create a local copy of the federated room if it doesn't exist
      // Use a system user ID for cross-platform rooms
      const systemUserId = new mongoose.Types.ObjectId('000000000000000000000000');
      room = new Room({
        roomId,
        name: `Federated Room ${roomId}`,
        creator: systemUserId,
        participants: [],
        federationSettings: {
          isEnabled: true,
          allowedPlatforms: ['sociality', 'telegram', 'discord'],
          registeredPeers: [],
          lastSyncAt: new Date()
        }
      });
      await room.save();
    }

    // Store the message locally with flexible field mapping
    const crossPlatformMessage = new CrossPlatformMessage({
      roomId,
      sender: message.from?.userId || message.from?.id || 'unknown',
      senderUsername: message.from?.displayName || message.from?.username || 'Unknown User',
      senderPlatform: message.from?.platform || 'unknown',
      text: message.text || "",
      img: message.img || "",
      file: message.file || "",
      fileName: message.fileName || "",
      fileSize: message.fileSize || 0,
      emoji: message.emoji || "",
      platform: message.from?.platform || 'unknown',
      messageId: message.id || Date.now().toString(),
      relayedFrom: originatingPlatform
    });
    await crossPlatformMessage.save();

    // Update room's last activity
    room.lastActivity = new Date();
    room.messageCount = (room.messageCount || 0) + 1;
    await room.save();

    // Emit the message to connected Sociality users via Socket.IO
    const io = req.app.get('io');
    if (io) {
      const socketMessage = {
        id: message.id || Date.now().toString(),
        text: message.text,
        img: message.img || undefined,
        file: message.file || undefined,
        fileName: message.fileName || undefined,
        fileSize: message.fileSize || undefined,
        emoji: message.emoji || undefined,
        sender: {
          _id: message.from?.userId || message.from?.id || 'unknown',
          username: message.from?.displayName || message.from?.username || 'Unknown User',
          platform: message.from?.platform || 'unknown'
        },
        timestamp: message.sentAt || message.timestamp || new Date().toISOString(),
        roomId,
        isCrossPlatform: true,
        platform: message.from?.platform || 'unknown'
      };

      console.log(`🔄 Emitting crossPlatformMessage to room_${roomId} from ${message.from?.platform}:`, socketMessage);
      io.to(`room_${roomId}`).emit('crossPlatformMessage', socketMessage);
    } else {
      console.warn('⚠️ Socket.IO instance not available for message relay');
    }

    res.json({
      success: true,
      message: 'Message relayed to Sociality users'
    });
  } catch (error) {
    console.error('Error handling direct relay message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to handle direct relay message',
      message: error.message
    });
  }
});

// Get Telegram binding for a room
router.get("/rooms/:roomId/telegram", async (req, res) => {
  try {
    const { roomId } = req.params;

    const binding = await TelegramBinding.findByRoomId(roomId);

    if (!binding) {
      return res.json({
        success: true,
        bound: false,
        message: 'No Telegram chat bound to this room'
      });
    }

    res.json({
      success: true,
      bound: true,
      binding: {
        telegramChatId: binding.telegramChatId,
        telegramChatType: binding.telegramChatType,
        telegramChatTitle: binding.telegramChatTitle,
        createdAt: binding.createdAt,
        messageCount: binding.messageCount,
        lastMessageAt: binding.lastMessageAt,
        createdBy: binding.createdBy
      }
    });
  } catch (error) {
    console.error('Error getting Telegram binding:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get Telegram binding',
      message: error.message
    });
  }
});

// Get all Telegram bindings (admin endpoint)
router.get("/telegram/bindings", protectRoute, async (req, res) => {
  try {
    const bindings = await TelegramBinding.find({ isActive: true })
      .sort({ createdAt: -1 })
      .limit(50);

    res.json({
      success: true,
      bindings: bindings.map(binding => ({
        roomId: binding.roomId,
        telegramChatId: binding.telegramChatId,
        telegramChatType: binding.telegramChatType,
        telegramChatTitle: binding.telegramChatTitle,
        createdAt: binding.createdAt,
        messageCount: binding.messageCount,
        lastMessageAt: binding.lastMessageAt,
        createdBy: binding.createdBy
      }))
    });
  } catch (error) {
    console.error('Error getting Telegram bindings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get Telegram bindings',
      message: error.message
    });
  }
});

// Initialize federation registration
registerWithFederation();

export default router;
